/** @type {import('tailwindcss').Config} */
module.exports = {
  // NOTE: Update this to include the paths to all files that contain Nativewind classes.
  content: ["./app/**/*.{js,jsx,ts,tsx}", "./components/**/*.{js,jsx,ts,tsx}"],
  presets: [require("nativewind/preset")],
  theme: {
    extend: {
      fontFamily: {
        'supreme': ['Supreme-Regular'],
        'supreme-bold': ['Supreme-Bold'],
        'supreme-extrabold': ['Supreme-Extrabold'],
        'supreme-thin': ['Supreme-Thin'],
        'supreme-light': ['Supreme-Light'],
        'supreme-medium': ['Supreme-Medium'],
      },
      colors: {
        primary_color: "#7E188E"
      },
    },
  },
  plugins: [],
}
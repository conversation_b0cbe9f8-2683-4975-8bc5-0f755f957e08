import { useFonts } from "expo-font";
import { Stack } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { useEffect, useState } from "react";
import "../global.css";

SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [loaded, error] = useFonts({
    "Supreme-Thin": require("../assets/fonts/Supreme-Thin.ttf"),
    "Supreme-Light": require("../assets/fonts/Supreme-Light.ttf"),
    "Supreme-Regular": require("../assets/fonts/Supreme-Regular.ttf"),
    "Supreme-Medium": require("../assets/fonts/Supreme-Medium.ttf"),
    "Supreme-Bold": require("../assets/fonts/Supreme-Bold.ttf"),
    "Supreme-Extrabold": require("../assets/fonts/Supreme-Extrabold.ttf"),
  });

  const [appIsReady, setAppIsReady] = useState(false);

  useEffect(() => {
    if (loaded || error) {
      setAppIsReady(true);
      SplashScreen.hideAsync();
    }
  }, [loaded, error]);

  // Fallback timeout in case fonts don't load
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (!appIsReady) {
        console.warn("Font loading timeout, proceeding without custom fonts");
        setAppIsReady(true);
        SplashScreen.hideAsync();
      }
    }, 5000); // 5 second timeout

    return () => clearTimeout(timeout);
  }, [appIsReady]);

  if (!appIsReady) {
    return null;
  }

  return (
    <Stack>
      <Stack.Screen name="index" options={{ headerShown: false, animation: "fade" }} />
      <Stack.Screen name="(dashboard)" options={{ headerShown: false, animation: "fade" }} />
      <Stack.Screen name="(onboarding)" options={{ headerShown: false, animation: "fade" }} />
      <Stack.Screen name="link-bank-account" options={{ headerShown: false, animation: "slide_from_right" }} />
    </Stack>
  );
}

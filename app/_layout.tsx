import { useFonts } from "expo-font";
import { Stack } from "expo-router";
import * as SplashScreen from 'expo-splash-screen';
import "../global.css";
import { useEffect } from "react";

SplashScreen.preventAutoHideAsync();

export default function RootLayout() {

  const [loaded] = useFonts({
    'Supreme-Thin': require('../assets/fonts/Supreme-Thin.ttf'),
    'Supreme-Light': require('../assets/fonts/Supreme-Light.ttf'),
    'Supreme-Regular': require('../assets/fonts/Supreme-Regular.ttf'),
    'Supreme-Medium': require('../assets/fonts/Supreme-Medium.ttf'),
    'Supreme-Bold': require('../assets/fonts/Supreme-Bold.ttf'),
    'Supreme-Extrabold': require('../assets/fonts/Supreme-Extrabold.ttf'),
  });

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  return (
    <Stack>
      <Stack.Screen name="index" options={{ headerShown: false, animation: "fade" }} />
      <Stack.Screen name="(dashboard)" options={{ headerShown: false, animation: "fade" }} />
      <Stack.Screen name="(onboarding)" options={{ headerShown: false, animation: "fade" }} />
    </Stack>
  );
}

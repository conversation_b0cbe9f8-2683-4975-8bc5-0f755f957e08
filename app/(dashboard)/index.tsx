import InvestImg from "@/assets/images/invest-img.svg";
import Button from "@/components/ui/Button";
import PaymentMethodModal from "@/components/ui/PaymentMethodModal";
import UserCards from "@/components/UserCards";
import { useRouter } from "expo-router";
import { Bell, BookMarked, Eye, HandCoins, HandHeart, PiggyBank, Settings } from "lucide-react-native";
import { useState } from "react";
import { ScrollView, Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

export default function Dashboard() {
  const router = useRouter();
  const [showPaymentModal, setShowPaymentModal] = useState(false);

  const userCardsList = [
    { name: "Microsavings", amount: "9,000.00", icon: <PiggyBank /> },
    { name: "Total Investments", amount: "9,000.00", icon: <HandCoins /> },
    { name: "Total Deposit", amount: "9,000.00", icon: <BookMarked /> },
  ];

  const servicesList = [
    { name: "Fund Transfer", icon: <PiggyBank /> },
    { name: "Add Money", icon: <HandCoins /> },
    { name: "Widthdraw", icon: <BookMarked /> },
    { name: "Microsave", icon: <BookMarked /> },
  ];

  const handleInvestNow = () => {
    setShowPaymentModal(true);
  };

  const handleLinkBankAccount = () => {
    setShowPaymentModal(false);
    router.push('/(ScreensWithNoTab)/dashboard/link-bank-account');
  };

  const handleAddPaymentMethod = () => {
    setShowPaymentModal(false);
    // Handle add payment method logic here
    console.log('Add payment method');
  };

  return (
    <SafeAreaView className="flex-1 bg-primary_color" edges={["top"]}>
      <ScrollView contentContainerStyle={{ flexGrow: 1 }} showsHorizontalScrollIndicator={false}>
        {/* Top Primary Section */}
        <View className="p-4 bg-primary_color">
          {/* Header */}
          <View className="flex flex-row items-center justify-between">
            <View>
              <Text className="text-2xl font-semibold text-bg_white">Hi, Dave !</Text>
            </View>

            <View className="flex flex-row gap-4">
              <TouchableOpacity className="bg-bg_white rounded-2xl p-3 border border-slate-300">
                <View className="relative">
                  <Bell size={20} />
                  <View className="absolute top-0 right-0 bg-green-600 h-2 w-2 rounded-full" />
                </View>
              </TouchableOpacity>

              <TouchableOpacity className="bg-bg_white rounded-2xl p-3 border border-slate-300">
                <Settings size={20} />
              </TouchableOpacity>
            </View>
          </View>

          {/* Balance */}
          <View className="items-center my-10">
            <Text className="text-base font-medium text-bg_white">Investment Balance</Text>
            <View className="flex-row items-center gap-4">
              <Text className="text-3xl font-semibold text-bg_white">$900,000.00</Text>
              <Eye color={"#FFFFFF"} />
            </View>
            <Text className="text-yellow-400 text-sm">0.35% • Gained in 1 month</Text>
          </View>

          {/* User Cards */}
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {userCardsList.map((item, index) => (
              <UserCards user={item} key={index} />
            ))}
          </ScrollView>
        </View>

        {/* White Background Section */}
        <View className="bg-white rounded-t-3xl mt-4 pt-4">

          <View className="m-4 flex-row justify-between gap-6 py-4 .bg-bg_gray_1 .shadow-xl">
            {servicesList.map((item, index) => (
              <View key={index} className="items-center">
                <TouchableOpacity className="items-center">
                  <View className="bg-white rounded-full p-4 shadow flex items-center justify-center">
                    {item.icon}
                  </View>
                  <Text className="text-sm font-medium mt-4">{item.name}</Text>
                </TouchableOpacity>
              </View>
            ))}
          </View>

          <View className="flex-1 flex-row gap-4 items-center justify-between bg-bg_gray_2 border border-gray-200 rounded-3xl p-6 mx-4 my-2">

            {/* Left column */}
            <View className="flex-1">
              <Text className="font-semibold text-lg mb-2">Hi there!</Text>
              <Text className="text-sm mb-2 text-gray-500">
                Join others using Reals and invest in fractional shares of real estate properties today.
                Let’s help you grow your wealth.
              </Text>

              <View className="flex-row">
                <Button
                  onPress={handleInvestNow}
                  title="Invest now"
                  variant="outline"
                  size="medium"
                />
              </View>
            </View>

            {/* Right column (image) */}
            <InvestImg />

          </View>


          <View className="flex-1 flex-row gap-4 items-center bg-bg_gray_2 border border-gray-200 rounded-3xl p-6 mx-4 my-2">

            <HandHeart color={"#B526CC"}/>

            <View className="flex-1">
              <Text className="font-semibold text-lg mb-2 text-primary_color_text">See your recent activities</Text>
              <Text className="text-sm mb-2 text-gray-500">
                View the most recent transactions you performed on landest.
              </Text>
            </View>

          </View>



        </View>
      </ScrollView>

      {/* Payment Method Modal */}
      <PaymentMethodModal
        visible={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        onLinkBankAccount={handleLinkBankAccount}
        onAddPaymentMethod={handleAddPaymentMethod}
      />
    </SafeAreaView>
  );
}

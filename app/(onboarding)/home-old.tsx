import Onb1 from "@/assets/images/onboarding/onb-1.svg";
import Button from "@/components/ui/Button";
import { Text, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

export default function Home() {
  return (
    <SafeAreaView className="relative p-10 flex-1 items-center justify-center">
      <View className="flex-1 items-center justify-between">

        <Onb1 />
        <View className="mt-4">
          <Text className="text-2xl font-semibold text-black">Welcome to Reals</Text>
          <Text className="text-sm font-semibold text-gray-400">Your gateway to fractional real estate investment and more! Let’s build wealth together.</Text>
        </View>

        <View className="flex-row items-center justify-between mt-4">
          <Button
            title="Skip"
            onPress={() => { }}
            variant="secondary"
            size="large"
            className="bg-transparent"
          />

          <Button
            title="Next"
            onPress={() => { }}
            variant="primary"
            size="large"
          />

        </View>
      </View>

    </SafeAreaView>
  );
}
import React, { useRef, useState } from "react";
import { Dimensions, FlatList, Text, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

// SVG imports
import Onb1 from "@/assets/images/onboarding/onb-1.svg";
import Onb2 from "@/assets/images/onboarding/onb-2.svg";
import Onb3 from "@/assets/images/onboarding/onb-3.svg";
import Button from "@/components/ui/Button";

import { useRouter } from "expo-router";
import { ArrowRight } from "lucide-react-native";

const { width } = Dimensions.get("window");

const slides = [
  {
    key: "1",
    title: "Welcome to Reals",
    text: "Your gateway to fractional real estate investment and more! Let’s build wealth together.",
    SvgImage: Onb1,
  },
  {
    key: "2",
    title: "Grow your wealth with us",
    text: "Reals allows you spread investment across different asset classes to manage risk.",
    SvgImage: Onb2,
  },
  {
    key: "3",
    title: "Monitor your investment",
    text: "Keep a close eye on your investment portfolio effortlessly with our intuitive monitoring tools.",
    SvgImage: Onb3,
  },
];

export default function OnboardingScreen({ navigation }: any) {
  const flatListRef = useRef<FlatList>(null);
  const [currentIndex, setCurrentIndex] = useState(0);

  const router = useRouter()

  const handleNext = () => {
    if (currentIndex < slides.length - 1) {
      flatListRef.current?.scrollToIndex({ index: currentIndex + 1 });
    }
  };

  const handleSkip = () => {
    flatListRef.current?.scrollToIndex({ index: slides.length - 1 });
  };

  const onViewableItemsChanged = useRef(({ viewableItems }: any) => {
    if (viewableItems.length > 0) {
      setCurrentIndex(viewableItems[0].index);
    }
  }).current;

  const renderItem = ({ item }: any) => {
    const ImageComp = item.SvgImage;
    return (
      <SafeAreaView className="flex-1 bg-white px-6 justify-between" style={{ width }}>
        <View className="flex-1 items-center justify-center">

          <ImageComp width={350} height={350} />

          <View className="flex-row gap-2 my-4">
            {slides.map((_, i) => (
              <View
                key={i}
                className={`h-3 w-3 rounded-full ${i === currentIndex ? "bg-primary_color" : "bg-gray-300"
                  }`}
              />
            ))}
          </View>

          <View className="flex items-start justify-start flex-col gap-4 my-4">
            <Text
              className="text-3xl text-black"
              style={{ fontFamily: 'Supreme-Extrabold' }}
            >
              {item.title}
            </Text>

            <Text
              className="text-lg text-gray-500 font-Supreme-Bold"
              style={{ fontFamily: 'Supreme-Regular' }}
            >
              {item.text}
            </Text>
        </View>
        </View>

        {item.key === "3" ? (
          <View className="mb-6 flex-col gap-4">
            <Button
              title="Sign Up"
              variant="primary"
              size="large"
              onPress={() => router.push("/(onboarding)/signup")}
              className="flex items-center gap-4"
              icon={
                <ArrowRight size={20} color={"white"} />
              }
            />

            <Button
              title="Sign In"
              variant="outline"
              size="large"
              onPress={() => router.push("/(onboarding)/signin")}
              className="flex items-center gap-4"
              icon={
                <ArrowRight size={20} color={"#7E188E"} />
              }
            />
          </View>
        ) : (
          <View className="flex-row justify-between mb-6">
            <Button
              title="Skip"
              variant="secondary"
              size="large"
              onPress={handleSkip}
              className="bg-transparent"
            />

            <Button
              title="Next"
              variant="primary"
              size="large"
              onPress={handleNext}
              className="flex items-center gap-4"
              icon={
                <ArrowRight size={20} color={"white"} />
              }
            />
          </View>
        )}
      </SafeAreaView>
    );
  };

  return (
    <FlatList
      ref={flatListRef}
      data={slides}
      horizontal
      pagingEnabled
      showsHorizontalScrollIndicator={false}
      keyExtractor={(item) => item.key}
      renderItem={renderItem}
      onViewableItemsChanged={onViewableItemsChanged}
      viewabilityConfig={{ viewAreaCoveragePercentThreshold: 50 }}
    />
  );
}

import TitleAndBack from "@/components/TitleAndBack";
import Button from "@/components/ui/Button";
import TextInput from "@/components/ui/Input";
import { useRouter } from "expo-router";
import { ArrowRight, ChevronLeft, Fingerprint } from "lucide-react-native";
import { Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";


export default function SignIn() {

  const router = useRouter();

  return (
    <SafeAreaView className="p-4 bg-bg_white h-full">

      <TitleAndBack
        title="Sign In"
        className="text-center flex flex-row items-center !justify-center"
        showBackContainer={false}
      />

      <View className="flex flex-col gap-6 mt-10">
        <TextInput
          label="Email"
          placeholder="<EMAIL>"
          keyboardType="email-address"
          autoCapitalize="none"
          labelClassName="font-medium"
        />

        <TextInput
          label="Password"
          placeholder="8 Character Max"
          labelClassName="font-medium"
          secureTextEntry
        />

      </View>

      <View className="mt-8">
        <Button
          title="Next"
          variant="primary"
          size="large"
          className="flex items-center gap-4"
          icon={
            <ArrowRight size={20} color={"#FFFFFF"} className="ml-4" />
          }
          onPress={() => router.push("/(onboarding)/verification")}
        />
      </View>

      <View className="flex items-center flex-col justify-center mt-10 gap-10">

        <View>
          <Fingerprint size={40} />
        </View>

        <TouchableOpacity className="flex items-center flex-row" onPress={() => router.push("/(onboarding)/signup")}>
          <Text className="text-gray-600 pr-1">Don&apos;t have an account?
          </Text>
          <Text className="font-medium text-primary_color">
            Sign Up
          </Text>
        </TouchableOpacity>

      </View>


    </SafeAreaView>
  )
}
import TitleAndBack from "@/components/TitleAndBack";
import Button from "@/components/ui/Button";
import TextInput from "@/components/ui/Input";
import { useRouter } from "expo-router";
import { ArrowRight, ChevronLeft } from "lucide-react-native";
import { Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";


export default function SignUp() {

  const router = useRouter();

  return (
    <SafeAreaView className="p-4 bg-bg_white h-full">

      <TitleAndBack
        title="Create an Account"
      />

      <View></View>

      <View className="mt-4">
        <Text
          className="text-lg text-gray-600"
          style={{ fontFamily: 'Supreme-Regular' }}
        >
          Welcome to Reals, kindly enter your email and password below to begin account creation
        </Text>
      </View>

      <View className="flex flex-col gap-6 mt-10">
        <TextInput
          label="Email"
          placeholder="<EMAIL>"
          keyboardType="email-address"
          autoCapitalize="none"
          labelClassName="font-medium"
        />

        <TextInput
          label="Password"
          placeholder="8 Character Max"
          labelClassName="font-medium"
          secureTextEntry
        />

      </View>

      <View className="mt-8">
        <Button
          title="Next"
          variant="primary"
          size="large"
          className="flex items-center gap-4"
          icon={
            <ArrowRight size={20} color={"#FFFFFF"} className="ml-4" />
          }
          onPress={() => router.push("/(onboarding)/verification")}
        />
      </View>

    </SafeAreaView>
  )
}
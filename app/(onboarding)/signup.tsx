import TextInput from "@/components/ui/Input";
import { useRouter } from "expo-router";
import { ChevronLeft } from "lucide-react-native";
import { TouchableOpacity } from "react-native";
import { Text, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";


export default function SignUp() {

  const router = useRouter();

  return (
    <SafeAreaView className="p-4">

      <View className="flex flex-row items-center justify-between">
        <TouchableOpacity
          className="w-12 h-12 rounded-xl border border-gray-300 flex items-center justify-center"
          onPress={() => router.back()}
        >
          <ChevronLeft size={20} color={"#6c6c6cff"} />
        </TouchableOpacity>

        <Text className="text-2xl font-semibold">Create an Account</Text>

        <View className="w-12" />
      </View>

      <View></View>

      <View className="mt-4">
        <Text
          className="text-lg text-gray-600"
        >Welcome to Landest, kindly enter your email and password below to begin account creation</Text>
      </View>

      <View className="flex flex-col gap-4 mt-10">

        <TextInput
          label="Email"
          placeholder="Enter your email"
          className="mt-4"
        />
      </View>

    </SafeAreaView>
  )
}
import TitleAndBack from "@/components/TitleAndBack";
import Button from "@/components/ui/Button";
import ProgressIndicator from "@/components/ui/ProgressIndicator";
import { useRouter } from "expo-router";
import { Send } from "lucide-react-native";
import { Text, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";


export default function Verification() {

  const router = useRouter();

  return (
    <SafeAreaView className="p-4 bg-bg_white h-full">
      <View className="flex-1 justify-between flex-col">

        <View>
          <TitleAndBack
            title="Verify your Email"
          />

          <ProgressIndicator
            totalSteps={6}
            currentStep={1}
            className="my-4"
          />
          <View className="flex flex-col gap-4 my-6">
            <Text
              className="text-base text-gray-600 text-center"
              style={{ fontFamily: 'Supreme-Regular' }}
            >
              Please check <Text className="font-semibold"><EMAIL></Text> and click on the link sent to verify your email address.        </Text>

            <Text className="text-primary_color text-center mt-4">Resend in 00:40S</Text>
          </View>

        </View>

        <View className="mt-8">
          <Button
            title="Open Mail"
            variant="primary"
            size="large"
            className="flex items-center gap-4"
            icon={
              <Send size={20} color={"#FFFFFF"} className="ml-4" />
            }
            onPress={() => router.push("/(onboarding)/signin")}
          />
        </View>

      </View>


    </SafeAreaView>
  )
}
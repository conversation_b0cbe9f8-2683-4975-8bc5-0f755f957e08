import TitleAndBack from "@/components/TitleAndBack";
import Button from "@/components/ui/Button";
import TextInput from "@/components/ui/Input";
import { useRouter } from "expo-router";
import { ArrowRight } from "lucide-react-native";
import { Text, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";


export default function ForgotPassword() {

  const router = useRouter();

  return (
    <SafeAreaView className="p-4 bg-bg_white h-full">

      <TitleAndBack
        title="Forgot Password"
      />

      <View className="flex flex-col gap-6 mt-10">
        <TextInput
          label="Email"
          placeholder="<EMAIL>"
          keyboardType="email-address"
          autoCapitalize="none"
          labelClassName="font-medium"
        />

      </View>

      <View className="mt-8">
        <Button
          title="Proceed"
          variant="primary"
          size="large"
          className="flex items-center gap-4"
          icon={
            <ArrowRight size={20} color={"#FFFFFF"} className="ml-4" />
          }
          onPress={() => router.push("/(onboarding)/signin")}
        />
      </View>


    </SafeAreaView>
  )
}
import TitleAndBack from "@/components/TitleAndBack";
import Button from "@/components/ui/Button";
import TextInput from "@/components/ui/Input";
import { useRouter } from "expo-router";
import { Send } from "lucide-react-native";
import { Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";


export default function ForgotPassword() {

  const router = useRouter();

  return (
    <SafeAreaView className="p-4 bg-bg_white h-full">

      <TitleAndBack
        title="Forgot Password"
      />

      <View className="flex flex-col gap-6 mt-10">
        <TextInput
          label="Email"
          placeholder="<EMAIL>"
          keyboardType="email-address"
          autoCapitalize="none"
          labelClassName="font-medium"
        />

      </View>

      <View className="mt-8">
        <Button
          title="Proceed"
          variant="primary"
          size="large"
          className="flex items-center gap-4"
          icon={
            <Send size={20} color={"#FFFFFF"} className="ml-4" />
          }
          onPress={() => router.push("/(onboarding)/signin")}
        />
      </View>

      <View className="items-center justify-center mt-6">
        <TouchableOpacity className="border-b flex items-center flex-row" onPress={() => router.push("/(onboarding)/signin")}>
          <Text className="font-medium text-primary_color">
            Back to Signin
          </Text>
        </TouchableOpacity>

      </View>


    </SafeAreaView>
  )
}
import { Redirect } from "expo-router";
import { useEffect, useState } from "react";
import { Image, Text, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

export default function Index() {
  const [loading, setLoading] = useState(false)

  useEffect(()=>{
    setTimeout(() => {
      setLoading(true);

    }, 3000);    
  },[loading])

  if(loading){
    return (
      <Redirect href="/(onboarding)/home"/>
    )
  }

  return (
    <SafeAreaView className="flex-1 items-center justify-center bg-primary_color">
      <Image
        source={require("@/assets/images/reel-logo.png")}
        className="w-20 h-20 object-contain"
      />
      <View className="mt-4">
        <Text className="text-3xl font-semibold text-white">Reals</Text>
      </View>
    </SafeAreaView>
  );
}

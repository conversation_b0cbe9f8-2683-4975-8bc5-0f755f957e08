import React from "react";
import { Dimensions, Text, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import Button from "@/components/ui/Button";

import { useRouter } from "expo-router";
import { ArrowRight, BadgeCheck } from "lucide-react-native";

const { width } = Dimensions.get("window");
export default function SuccessLinked() {
  const router = useRouter();

  return (
    <SafeAreaView className="flex-1 bg-white px-6 justify-between" style={{ width }}>
      <View className="flex-1 items-center justify-center">

        <View className="rotate-45 rounded-[30%] h-40 w-40 flex items-center justify-center bg-primary_color">
          <BadgeCheck size={100} color={"#FFFFFF"} />
        </View>

        <View className="flex items-center justify-center flex-col gap-4 my-10">
          <Text
            className="text-3xl text-black font-semibold"
            style={{ fontFamily: 'Supreme-Extrabold' }}
          >
            Congratulations !
          </Text>

          <Text
            className="text-center text-lg text-gray-500 font-supreme-regular"
          >
            Account number <Text className="font-semibold text-black">********....</Text> has been successfully linked to your wallet.
          </Text>
        </View>
      </View>

      <View className="mb-6 flex-col gap-4">

        <Button
          title="Proceed to Transfer Funds"
          variant="primary"
          size="large"
          onPress={() => router.push("/(ScreensWithNoTab)/dashboard/fund-transfer")}
          className="flex items-center gap-4"
          icon={
            <ArrowRight size={20} color={"#FFFFFF"} />
          }
        />
      </View>
    </SafeAreaView>
  );
}

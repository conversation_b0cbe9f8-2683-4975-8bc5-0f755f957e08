import TitleAndBack from '@/components/TitleAndBack';
import Button from '@/components/ui/Button';
import TextInput from '@/components/ui/Input';
import { useRouter } from 'expo-router';
import { ArrowRight } from 'lucide-react-native';
import React from 'react';
import { Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function LinkBankAccount() {
  const router = useRouter();

  const handleLink = () => {
    // Handle bank account linking logic here
    console.log('Linking bank account...');
    // Navigate back to dashboard or show success message
    router.push("/(ScreensWithNoTab)/dashboard/sucess-linked");
  };

  return (
    <SafeAreaView className="flex-1 bg-bg_white">
      <View className="flex-1 p-4">
        {/* Header */}
        <TitleAndBack
          title="Link Bank Account"
          onBack={() => router.back()}
        />

        {/* Description */}
        <View className="mt-6 mb-8">
          <Text
            className="text-base text-gray-600 leading-6"
            style={{ fontFamily: 'Supreme-Regular' }}
          >
            Please provide your account name, account number and account type to link your bank account.
          </Text>
        </View>

        {/* Form */}
        <View className="flex-1">
          <View className="flex flex-col gap-6">
            <TextInput
              label="Account Holder Name"
              placeholder="Enter account holder name"
              labelClassName="font-medium"
            />

            <TextInput
              label="Account Number"
              placeholder="Enter account number"
              keyboardType="numeric"
              labelClassName="font-medium"
            />

            <TextInput
              label="Bank Name"
              placeholder="Enter bank name"
              labelClassName="font-medium"
            />

            <TextInput
              label="Account Type"
              placeholder="Select account type"
              labelClassName="font-medium"
            />
          </View>

          {/* Link Button */}
          <View className="mt-auto">
            <Button
              title="Link"
              variant="primary"
              size="large"
              onPress={handleLink}
              icon={<ArrowRight size={20} color="#FFFFFF" />}
            />
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
}

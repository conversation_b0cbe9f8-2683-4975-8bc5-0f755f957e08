import TitleAndBack from '@/components/TitleAndBack';
import Button from '@/components/ui/Button';
import TextInput from '@/components/ui/Input';
import { useRouter } from 'expo-router';
import { ArrowRight } from 'lucide-react-native';
import React from 'react';
import { View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function FundTransfer() {
  const router = useRouter();

  const handleLink = () => {
    // Handle bank account linking logic here
    console.log('fund sucess...');
    // Navigate back to dashboard or show success message
    router.push("/(dashboard)");
  };

  return (
    <SafeAreaView className="flex-1 bg-bg_white">
      <View className="flex-1 p-4">
        {/* Header */}
        <TitleAndBack
          title="Funds Transfer"
          onBack={() => router.back()}
        />

        {/* Form */}
        <View className="flex-1 mt-10">
          <View className="flex flex-col gap-6">
            <TextInput
              label="Enter Amount"
              placeholder="#200,000"
              labelClassName="font-medium"
            />

            <TextInput
              label="Enter Transaction Code"
              placeholder="4573"
              keyboardType="numeric"
              labelClassName="font-medium"
            />
          </View>

          {/* Link Button */}
          <View className="mt-auto">
            <Button
              title="Fund"
              variant="primary"
              size="large"
              onPress={handleLink}
              icon={<ArrowRight size={20} color="#FFFFFF" />}
            />
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
}

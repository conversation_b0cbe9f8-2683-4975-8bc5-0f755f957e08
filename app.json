{"expo": {"name": "<PERSON><PERSON>", "slug": "<PERSON><PERSON>", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "reels", "userInterfaceStyle": "automatic", "newArchEnabled": true, "fonts": ["./assets/fonts/Supreme-Thin.ttf", "./assets/fonts/Supreme-Light.ttf", "./assets/fonts/Supreme-Regular.ttf", "./assets/fonts/Supreme-Medium.ttf", "./assets/fonts/Supreme-Bold.ttf", "./assets/fonts/Supreme-Extrabold.ttf"], "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}}}
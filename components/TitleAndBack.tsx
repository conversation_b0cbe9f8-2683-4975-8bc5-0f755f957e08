import { useRouter } from "expo-router";
import { ChevronLeft } from "lucide-react-native";
import { TouchableOpacity, View } from "react-native";
import { Text } from "react-native";

interface TitleAndBackProps {
  title?: string;
  className?: string;
  showBackContainer?: boolean;
}

export default function TitleAndBack({
  title,
  className,
  showBackContainer = true
}: TitleAndBackProps) {

  const router = useRouter();

  return (

    <View className={`flex flex-row items-center justify-between ${className}`}>
      {showBackContainer && (
        <TouchableOpacity
          className="w-12 h-12 rounded-xl border border-gray-300 flex items-center justify-center"
          onPress={() => router.back()}
        >
          <ChevronLeft size={20} color={"#6c6c6cff"} />
        </TouchableOpacity>
      )}

      <Text
        className="text-2xl font-semibold"
        style={{ fontFamily: 'Supreme-Bold' }}
      >
        {title}
      </Text>

      <View className="w-12" />
    </View>
  );
}

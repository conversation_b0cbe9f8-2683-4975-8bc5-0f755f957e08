import { useRouter } from "expo-router";
import { ChevronLeft } from "lucide-react-native";
import { Text, TouchableOpacity, View } from "react-native";

interface TitleAndBackProps {
  title?: string;
  className?: string;
  showBackContainer?: boolean;
  onBack?: () => void;
}

export default function TitleAndBack({
  title,
  className,
  showBackContainer = true,
  onBack
}: TitleAndBackProps) {

  const router = useRouter();

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      router.back();
    }
  };

  return (

    <View className={`flex flex-row items-center justify-between ${className}`}>
      {showBackContainer && (
        <TouchableOpacity
          className="w-12 h-12 rounded-xl border border-gray-300 flex items-center justify-center"
          onPress={handleBack}
        >
          <ChevronLeft size={20} color={"#6c6c6cff"} />
        </TouchableOpacity>
      )}

      <Text
        className="text-2xl font-semibold"
        style={{ fontFamily: 'Supreme-Bold' }}
      >
        {title}
      </Text>

      <View className="w-12" />
    </View>
  );
}

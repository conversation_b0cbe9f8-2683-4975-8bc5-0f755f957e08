// UserCards.tsx
import { BadgePoundSterling } from "lucide-react-native";
import { Image, Text, TouchableOpacity, View } from "react-native";

export default function UserCards({ user }: any) {
  return (
    <View className="w-72 h-40 mr-4 bg-gray-100 rounded-2xl p-4 px-6 shadow-sm flex justify-center flex-col">
      <View className="h-12 w-12 bg-yellow-500 flex items-center justify-center p-4 rounded-full">
        {user.icon}
      </View>
      <View>
        <Text className="text-lg font-medium text-gray-600">{user.name}</Text>
        <Text className="text-3xl font-semibold text-primary_color_text">₦{user.amount}</Text>
      </View>
    </View>
  );
};
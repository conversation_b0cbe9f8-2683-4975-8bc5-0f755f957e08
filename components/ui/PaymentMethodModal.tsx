import React from 'react';
import { Modal, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Building2, Plus, ChevronRight } from 'lucide-react-native';

interface PaymentMethodModalProps {
  visible: boolean;
  onClose: () => void;
  onLinkBankAccount: () => void;
  onAddPaymentMethod: () => void;
}

export default function PaymentMethodModal({
  visible,
  onClose,
  onLinkBankAccount,
  onAddPaymentMethod,
}: PaymentMethodModalProps) {
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View className="flex-1 justify-end bg-black/50">
        <View className="bg-white rounded-t-3xl">
          <SafeAreaView edges={['bottom']}>
            <View className="p-6">
              {/* Header */}
              <View className="items-center mb-6">
                <View className="w-12 h-1 bg-gray-300 rounded-full mb-4" />
                <Text 
                  className="text-xl text-gray-800"
                  style={{ fontFamily: 'Supreme-Bold' }}
                >
                  Select Payment Method
                </Text>
              </View>

              {/* Payment Options */}
              <View className="flex flex-col gap-6">
                {/* Link Bank Account */}
                <TouchableOpacity
                  className="flex-row items-center justify-between p-4 border border-gray-200 rounded-xl"
                  onPress={onLinkBankAccount}
                  activeOpacity={0.7}
                >
                  <View className="flex-row items-center">
                    <View className="w-12 h-12 bg-gray-100 rounded-xl items-center justify-center mr-4">
                      <Building2 size={24} color="#6B7280" />
                    </View>
                    <Text 
                      className="text-base text-gray-800"
                      style={{ fontFamily: 'Supreme-Medium' }}
                    >
                      Link bank account
                    </Text>
                  </View>
                  <ChevronRight size={20} color="#6B7280" />
                </TouchableOpacity>

                {/* Add Payment Method */}
                <TouchableOpacity
                  className="flex-row items-center justify-between p-4 border border-gray-200 rounded-xl"
                  onPress={onAddPaymentMethod}
                  activeOpacity={0.7}
                >
                  <View className="flex-row items-center">
                    <View className="w-12 h-12 bg-gray-100 rounded-xl items-center justify-center mr-4">
                      <Plus size={24} color="#6B7280" />
                    </View>
                    <Text 
                      className="text-base text-gray-800"
                      style={{ fontFamily: 'Supreme-Medium' }}
                    >
                      Add payment method
                    </Text>
                  </View>
                  <ChevronRight size={20} color="#6B7280" />
                </TouchableOpacity>
              </View>

              {/* Close button area for gesture */}
              <TouchableOpacity
                className="mt-6 py-4"
                onPress={onClose}
                activeOpacity={0.7}
              >
                <Text 
                  className="text-center text-gray-500"
                  style={{ fontFamily: 'Supreme-Regular' }}
                >
                  Cancel
                </Text>
              </TouchableOpacity>
            </View>
          </SafeAreaView>
        </View>
      </View>
    </Modal>
  );
}

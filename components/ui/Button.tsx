import React from 'react';
import { Text, TouchableOpacity } from 'react-native';

export type ButtonVariant = 'primary' | 'secondary' | 'outline';
export type ButtonSize = 'small' | 'medium' | 'large';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: ButtonVariant;
  size?: ButtonSize;
  disabled?: boolean;
  icon?: React.ReactNode;
  className?: string;
  textClassName?: string;
}

export default function Button({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  icon,
  className,
  textClassName,
}: ButtonProps) {
  const getButtonClasses = () => {
    let baseClasses = 'rounded-lg flex-row items-center justify-center min-h-12';

    // Size classes
    switch (size) {
      case 'small':
        baseClasses += ' py-2 px-4';
        break;
      case 'large':
        baseClasses += ' py-5 px-6';
        break;
      default: // medium
        baseClasses += ' py-3 px-5';
    }

    // Variant classes
    switch (variant) {
      case 'primary':
        baseClasses += ' bg-primary_color';
        break;
      case 'secondary':
        baseClasses += ' bg-gray-100';
        break;
      case 'outline':
        baseClasses += ' bg-transparent border border-primary_color_text';
        break;
    }

    if (disabled) {
      baseClasses += ' opacity-50';
    }

    return baseClasses;
  };

  const getTextClasses = () => {
    let baseTextClasses = 'text-center';

    // Font family - using font-medium as closest to Supreme-Medium
    baseTextClasses += ' font-medium';

    // Size text classes
    switch (size) {
      case 'small':
        baseTextClasses += ' text-sm';
        break;
      case 'large':
        baseTextClasses += ' text-lg';
        break;
      default: // medium
        baseTextClasses += ' text-base';
    }

    // Variant text classes
    switch (variant) {
      case 'primary':
        baseTextClasses += ' text-white';
        break;
      case 'secondary':
        baseTextClasses += ' text-gray-800';
        break;
      case 'outline':
        baseTextClasses += ' text-primary_color_text';
        break;
    }

    return baseTextClasses;
  };

  return (
    <TouchableOpacity
      className={`${getButtonClasses()} ${className || ''}`}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.8}
    >
      <Text
        className={`${getTextClasses()} ${textClassName || ''} ${icon ? 'ml-2' : ''}`}
        style={{ fontFamily: 'Supreme-Medium' }}
      >
        {title}
      </Text>
      {icon && <>{icon}</>}
    </TouchableOpacity>
  );
}

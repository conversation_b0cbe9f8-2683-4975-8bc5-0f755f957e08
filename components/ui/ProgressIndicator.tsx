import React from 'react';
import { View } from 'react-native';

interface ProgressIndicatorProps {
  totalSteps: number;
  currentStep: number;
  className?: string;
}

export default function ProgressIndicator({
  totalSteps,
  currentStep,
  className = '',
}: ProgressIndicatorProps) {
  return (
    <View className={`flex-row items-center justify-center ${className}`}>
      {Array.from({ length: totalSteps }, (_, index) => {
        const stepNumber = index + 1;
        const isActive = stepNumber <= currentStep;
        const isLast = index === totalSteps - 1;

        return (
          <View key={index} className="flex-row items-center">
            {/* Circle */}
            <View
              className={`w-5 h-5  rounded-full border-2 ${
                isActive
                  ? 'bg-primary_color border-primary_color'
                  : 'bg-transparent border-gray-300'
              }`}
            />
            
            {/* Connecting Line */}
            {!isLast && (
              <View
                className={`h-0.5 w-8 ${
                  stepNumber < currentStep
                    ? 'bg-primary_color'
                    : 'bg-gray-300'
                }`}
              />
            )}
          </View>
        );
      })}
    </View>
  );
}

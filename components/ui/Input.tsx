import React from 'react';
import { Text, TextInput as RNTextInput, TextInputProps, View } from 'react-native';

interface CustomTextInputProps extends TextInputProps {
  label?: string;
  error?: string;
  containerClassName?: string;
  inputClassName?: string;
}

export default function TextInput({
  label,
  error,
  containerClassName = '',
  inputClassName = '',
  ...props
}: CustomTextInputProps) {
  const baseInputClasses = 'border border-gray-400 rounded-lg px-4 py-3 bg-bg_white';
  const errorInputClasses = error ? 'border-red-500' : '';

  return (
    <View className={`${containerClassName}`}>
      {label && (
        <Text className="text-gray-700 font-medium mb-2">
          {label}
        </Text>
      )}
      <RNTextInput
        className={`${baseInputClasses} ${errorInputClasses} ${inputClassName}`}
        placeholderTextColor="#656667ff"
        {...props}
      />
      {error && (
        <Text className="text-red-500 text-sm mt-1">
          {error}
        </Text>
      )}
    </View>
  );
}
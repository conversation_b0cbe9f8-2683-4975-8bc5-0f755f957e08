import { Eye, EyeClosed, EyeOff } from 'lucide-react-native';
import React, { useState } from 'react';
import { TextInput as RNTextInput, Text, TextInputProps, TouchableOpacity, View } from 'react-native';

interface CustomTextInputProps extends TextInputProps {
  label?: string;
  error?: string;
  containerClassName?: string;
  inputClassName?: string;
  labelClassName?: string;
}

export default function TextInput({
  label,
  error,
  containerClassName = '',
  inputClassName = '',
  labelClassName='',
  secureTextEntry,
  ...props
}: CustomTextInputProps) {
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const isPasswordField = secureTextEntry !== undefined;

  const baseInputClasses =
    'border border-gray-400 rounded-xl px-4 bg-bg_white text-base h-16';
  const errorInputClasses = error ? 'border-red-500' : '';
  const passwordInputClasses = isPasswordField ? 'pr-12' : '';

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  return (
    <View className={`${containerClassName}`}>
      {label && (
        <Text
          className={`text-gray-700 mb-2 ${labelClassName}`}
          style={{ fontFamily: 'Supreme-Medium' }}
        >
          {label}
        </Text>
      )}
      <View className="relative">
        <RNTextInput
          className={`${baseInputClasses} ${errorInputClasses} ${passwordInputClasses} ${inputClassName}`}
          placeholderTextColor="#656667ff"
          style={{
            fontFamily: 'Supreme-Regular',
            textAlignVertical: 'center',
            paddingTop: 0,
            paddingBottom: 0,
            includeFontPadding: false,
          }}
          secureTextEntry={isPasswordField ? !isPasswordVisible : false}
          {...props}
        />
        {isPasswordField && (
          <TouchableOpacity
            className="absolute right-4 top-0 bottom-0 justify-center"
            onPress={togglePasswordVisibility}
            activeOpacity={0.7}
          >
            {isPasswordVisible ? (
              <EyeClosed size={20} color="#656667ff" />
            ) : (
              <Eye size={20} color="#656667ff" />
            )}
          </TouchableOpacity>
        )}
      </View>
      {error && (
        <Text
          className="text-red-500 text-sm mt-1"
          style={{ fontFamily: 'Supreme-Regular' }}
        >
          {error}
        </Text>
      )}
    </View>
  );
}

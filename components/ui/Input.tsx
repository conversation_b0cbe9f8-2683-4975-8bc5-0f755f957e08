import React from 'react';
import { TextInput as RNTextInput, Text, TextInputProps, View } from 'react-native';

interface CustomTextInputProps extends TextInputProps {
  label?: string;
  error?: string;
  containerClassName?: string;
  inputClassName?: string;
  labelClassName?: string;
}

export default function TextInput({
  label,
  error,
  containerClassName = '',
  inputClassName = '',
  labelClassName='',
  ...props
}: CustomTextInputProps) {
  const baseInputClasses =
    'border border-gray-400 rounded-xl px-4 bg-bg_white text-base h-16';
  const errorInputClasses = error ? 'border-red-500' : '';

  return (
    <View className={`${containerClassName}`}>
      {label && (
        <Text
          className={`text-gray-700 mb-2 ${labelClassName}`}
          style={{ fontFamily: 'Supreme-Medium' }}
        >
          {label}
        </Text>
      )}
      <RNTextInput
        className={`${baseInputClasses} ${errorInputClasses} ${inputClassName}`}
        placeholderTextColor="#656667ff"
        style={{
          fontFamily: 'Supreme-Regular',
          textAlignVertical: 'center', // Ensures vertical centering
        }}
        {...props}
      />
      {error && (
        <Text
          className="text-red-500 text-sm mt-1"
          style={{ fontFamily: 'Supreme-Regular' }}
        >
          {error}
        </Text>
      )}
    </View>
  );
}
